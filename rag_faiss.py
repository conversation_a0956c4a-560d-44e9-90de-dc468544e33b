import os
import numpy as np
import faiss
from openai import OpenAI  # 假设你用的是OpenAI官方SDK或者百炼兼容的SDK
from dotenv import load_dotenv  # 加载环境变量

# 加载当前目录下的.env文件（自动查找，无需指定路径）
load_dotenv() 

# 1. 初始化客户端
client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 环境变量中读取API Key，方便管理
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"  # 百炼服务base_url
)

# 2. 定义获取通义千问Embedding的函数
def get_tongyi_embedding(text):
    response = client.embeddings.create(
        model="text-embedding-v1",
        input=text
    )
    embedding = response.data[0].embedding
    return np.array(embedding, dtype=np.float32)

# 3. 创建FAISS索引
dimension = 1536
index = faiss.IndexFlatL2(dimension)

# 4. 准备文本向量列表
texts = ["我想看喜剧片", "有没有功夫电影", "推荐几部悬疑电影"]
vectors = np.array([get_tongyi_embedding(t) for t in texts])

print(vectors)
# 5. 添加向量进索引
index.add(vectors)
print("数据库中共有向量：", index.ntotal)

# 6. 测试查询
query = "推荐动作片电影"
query_vec = get_tongyi_embedding(query)
D, I = index.search(np.array([query_vec]), k=2)

print("相似度距离：", D)
print("匹配索引：", I)
print("匹配结果：", [texts[i] for i in I[0]])
