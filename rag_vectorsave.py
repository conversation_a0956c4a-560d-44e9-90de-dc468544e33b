import faiss
import numpy as np
import pickle

### 保存FAISS索引
# 创建示例向量数据（假设有3个128维向量）
vectors = np.random.random((3, 128)).astype('float64')

# 创建一个FAISS索引（这里使用最简单的IndexFlatL2索引）
dimension = 128
index = faiss.IndexFlatL2(dimension)

# 添加向量到索引
index.add(vectors)

### 保存文本映射数据
# 将索引保存到文件
faiss.write_index(index, "my_faiss_index.faiss")

print("FAISS索引已保存到 my_faiss_index.faiss")

texts = ["我想看喜剧片", "有没有搞笑电影", "推荐几部好电影"]

# 将文本列表保存到文件
with open("text_mapping.pkl", "wb") as f:
    pickle.dump(texts, f)

print("文本映射已保存到 text_mapping.pkl")

### 读取FAISS索引
loaded_index = faiss.read_index("my_faiss_index.faiss")
print(f"索引加载成功，当前向量数量：{loaded_index.ntotal}")

### 读取文本映射数据
with open("text_mapping.pkl", "rb") as f:
    loaded_texts = pickle.load(f)

print(f"加载的文本列表：{loaded_texts}")

### 使用保存的知识库示例
# 假设query_vec是查询文本的向量（float32 numpy数组，维度与索引一致）
query_vec = np.random.random((128,)).astype('float32')

k = 3  # 取最相似的3个结果
D, I = loaded_index.search(np.array([query_vec]), k)

print("距离：", D)
print("索引位置：", I)

for idx in I[0]:
    print(f"匹配文本：{loaded_texts[idx]}")