
import os
from openai import OpenAI
from dotenv import load_dotenv  # 加载环境变量

# 加载当前目录下的.env文件（自动查找，无需指定路径）
load_dotenv()  

def get_embedding(text):
    client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处用您的API Key进行替换
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"  # 百炼服务的base_url
    )

    completion = client.embeddings.create(
        model="text-embedding-v3",
        input=text,
        dimensions=1024,
        encoding_format="float"
    )

    # print(completion.model_dump_json())

    return completion.data


em = get_embedding('这是一个embedding的示例')

print(em)