#!/usr/bin/python
# -*- coding: utf-8 -*-
 
'''
Translated By Achen
2017/9
===
ZetCode wxPython tutorial
 
In this example, we create two horizontal 
toolbars. 
 
author: <PERSON>
website: www.zetcode.com
last modified: September 2011
'''
 
import wx
 
class Example(wx.Frame):
     
    def __init__(self, *args, **kwargs):
        super(Example, self).__init__(*args, **kwargs) 
             
        self.InitUI()
         
    def InitUI(self):    
 
        vbox = wx.BoxSizer(wx.VERTICAL)
 
        toolbar1 = wx.ToolBar(self)
        toolbar1.AddTool(wx.ID_ANY, 'new', wx.Bitmap('icons/new.png'))
        toolbar1.AddTool(wx.ID_ANY, 'open', wx.Bitmap('icons/open.png'))
        toolbar1.AddTool(wx.ID_ANY, 'save', wx.Bitmap('icons/save.png'))
        toolbar1.Realize()
 
        toolbar2 = wx.ToolBar(self)
        qtool = toolbar2.AddLabelTool(wx.ID_EXIT, 'exit', wx.Bitmap('icons/texit.png'))
        toolbar2.Realize()
 
        vbox.Add(toolbar1, 0, wx.EXPAND)
        vbox.Add(toolbar2, 0, wx.EXPAND)
 
        self.Bind(wx.EVT_TOOL, self.OnQuit, qtool)
         
        self.SetSizer(vbox)
 
        self.SetSize((300, 250))
        self.SetTitle('Toolbars')
        self.Centre()
        self.Show(True)
         
    def OnQuit(self, e):
        self.Close()
 
def main():
     
    ex = wx.App()
    Example(None)
    ex.MainLoop()    
 
 
if __name__ == '__main__':
    main()