#!/usr/bin/python
# -*- coding: utf-8 -*-
 
'''
Translated By achen
2017/0
===
ZetCode wxPython tutorial
 
This example shows a simple menu.
 
author: <PERSON>
website: www.zetcode.com
last modified: September 2011
'''
 
import wx
 
class Example(wx.Frame):
     
    def __init__(self, *args, **kwargs):
        super(Example, self).__init__(*args, **kwargs) 
             
        self.InitUI()
         
    def InitUI(self):    
 
        menubar = wx.MenuBar()
        fileMenu = wx.Menu()
        fitem = fileMenu.Append(wx.ID_EXIT, 'Quit', 'Quit application')
        menubar.Append(fileMenu, '&File')
        self.SetMenuBar(menubar)
         
        self.Bind(wx.EVT_MENU, self.OnQuit, fitem)
 
        self.SetSize((300, 200))
        self.SetTitle('Simple menu')
        self.Centre()
        self.Show(True)
         
    def OnQuit(self, e):
        self.Close()
 
def main():
     
    ex = wx.App()
    Example(None)
    ex.<PERSON>Loop()    
 
 
if __name__ == '__main__':
    main()