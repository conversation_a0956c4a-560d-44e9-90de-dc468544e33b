#!/usr/bin/python
# -*- coding: utf-8 -*-
 
'''
Translated By Achen
2017/9
===
ZetCode wxPython tutorial
 
In this example, we create a context menu.
 
author: <PERSON>
website: www.zetcode.com
last modified: September 2011
'''
 
import wx
 
class MyPopupMenu(wx.Menu):
     
    def __init__(self, parent):
        super(MyPopupMenu, self).__init__()
         
        self.parent = parent
 
        mmi = wx.MenuItem(self, wx.NewId(), 'Minimize')
        self.AppendItem(mmi)
        self.Bind(wx.EVT_MENU, self.OnMinimize, mmi)
 
        cmi = wx.MenuItem(self, wx.NewId(), 'Close')
        self.AppendItem(cmi)
        self.Bind(wx.EVT_MENU, self.OnClose, cmi)
 
 
    def OnMinimize(self, e):
        self.parent.Iconize()
 
    def OnClose(self, e):
        self.parent.Close()
         
 
class Example(wx.Frame):
     
    def __init__(self, *args, **kwargs):
        super(Example, self).__init__(*args, **kwargs) 
             
        self.InitUI()
         
    def InitUI(self):
 
        self.Bind(wx.EVT_RIGHT_DOWN, self.OnRightDown)
 
        self.SetSize((250, 200))
        self.SetTitle('Context menu')
        self.Centre()
        self.Show(True)
         
    def OnRightDown(self, e):
        self.PopupMenu(MyPopupMenu(self), e.GetPosition())
 
def main():
     
    ex = wx.App()
    Example(None)
    ex.MainLoop()    
 
 
if __name__ == '__main__':
    main()