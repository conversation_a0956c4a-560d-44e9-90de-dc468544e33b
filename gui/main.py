# First things, first. Import the wxPython package.
import wx

class Example(wx.Frame):

    def __init__(self, parent, title):
        super(Example, self).__init__(parent, title=title,
            size=(800, 600))

        # 创建菜单栏
        self.create_menu_bar()

        # 创建主面板
        self.create_main_panel()

        # 创建状态栏
        self.CreateStatusBar()
        self.SetStatusText("欢迎使用 wxPython 应用程序 - 菜单栏应显示在屏幕顶部")

        self.Center()
        self.Show()

    def create_main_panel(self):
        """创建主面板内容"""
        panel = wx.Panel(self)

        # 创建垂直布局
        vbox = wx.BoxSizer(wx.VERTICAL)

        # 添加标题
        title = wx.StaticText(panel, label="wxPython 应用程序")
        title_font = title.GetFont()
        title_font.PointSize += 8
        title_font = title_font.Bold()
        title.SetFont(title_font)

        # 添加说明文本
        info_text = wx.StaticText(panel, label="""
在 macOS 系统上：
• 菜单栏会显示在屏幕顶部的系统菜单栏中
• 当此应用程序处于活动状态时，你应该能在屏幕顶部看到：
  文件、编辑、视图、帮助 等菜单
• 尝试点击屏幕顶部的菜单项来测试功能
        """)

        # 添加按钮
        test_button = wx.Button(panel, label="测试按钮")
        test_button.Bind(wx.EVT_BUTTON, self.on_test_button)

        # 布局
        vbox.Add(title, 0, wx.ALL | wx.CENTER, 20)
        vbox.Add(info_text, 0, wx.ALL | wx.EXPAND, 20)
        vbox.Add(test_button, 0, wx.ALL | wx.CENTER, 20)

        panel.SetSizer(vbox)

    def on_test_button(self, event):
        """测试按钮事件处理"""
        wx.MessageBox('测试按钮被点击了！\n\n请查看屏幕顶部的菜单栏。', '测试', wx.OK | wx.ICON_INFORMATION)

    def create_menu_bar(self):
        """创建菜单栏"""
        # 创建菜单栏
        menubar = wx.MenuBar()

        # 创建文件菜单
        file_menu = wx.Menu()

        # 添加菜单项 - 在 macOS 上 wxPython 会自动处理 Ctrl 到 Cmd 的转换
        new_item = file_menu.Append(wx.ID_NEW, '新建\tCtrl+N', '创建新文件')
        open_item = file_menu.Append(wx.ID_OPEN, '打开\tCtrl+O', '打开文件')
        save_item = file_menu.Append(wx.ID_SAVE, '保存\tCtrl+S', '保存文件')
        file_menu.AppendSeparator()  # 分隔线
        exit_item = file_menu.Append(wx.ID_EXIT, '退出\tCtrl+Q', '退出应用程序')

        # 创建编辑菜单
        edit_menu = wx.Menu()
        copy_item = edit_menu.Append(wx.ID_COPY, '复制\tCtrl+C', '复制选中内容')
        paste_item = edit_menu.Append(wx.ID_PASTE, '粘贴\tCtrl+V', '粘贴内容')

        # 创建视图菜单（添加一些窗口内容）
        view_menu = wx.Menu()
        toolbar_item = view_menu.AppendCheckItem(wx.ID_ANY, '显示工具栏', '显示或隐藏工具栏')

        # 创建帮助菜单
        help_menu = wx.Menu()
        about_item = help_menu.Append(wx.ID_ABOUT, '关于', '关于此应用程序')

        # 将菜单添加到菜单栏
        menubar.Append(file_menu, '文件')
        menubar.Append(edit_menu, '编辑')
        menubar.Append(view_menu, '视图')
        menubar.Append(help_menu, '帮助')

        # 设置菜单栏 - 这在 macOS 上会将菜单显示在系统菜单栏中
        self.SetMenuBar(menubar)

        # 绑定事件
        self.Bind(wx.EVT_MENU, self.on_new, new_item)
        self.Bind(wx.EVT_MENU, self.on_open, open_item)
        self.Bind(wx.EVT_MENU, self.on_save, save_item)
        self.Bind(wx.EVT_MENU, self.on_exit, exit_item)
        self.Bind(wx.EVT_MENU, self.on_copy, copy_item)
        self.Bind(wx.EVT_MENU, self.on_paste, paste_item)
        self.Bind(wx.EVT_MENU, self.on_about, about_item)

    def on_new(self, event):
        """新建文件事件处理"""
        wx.MessageBox('新建文件功能', '信息', wx.OK | wx.ICON_INFORMATION)

    def on_open(self, event):
        """打开文件事件处理"""
        with wx.FileDialog(self, "打开文件",
                          wildcard="所有文件 (*.*)|*.*",
                          style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            pathname = fileDialog.GetPath()
            self.SetStatusText(f"已选择文件: {pathname}")

    def on_save(self, event):
        """保存文件事件处理"""
        with wx.FileDialog(self, "保存文件",
                          wildcard="所有文件 (*.*)|*.*",
                          style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            pathname = fileDialog.GetPath()
            self.SetStatusText(f"文件已保存到: {pathname}")

    def on_exit(self, event):
        """退出应用程序事件处理"""
        self.Close(True)

    def on_copy(self, event):
        """复制事件处理"""
        wx.MessageBox('复制功能', '信息', wx.OK | wx.ICON_INFORMATION)

    def on_paste(self, event):
        """粘贴事件处理"""
        wx.MessageBox('粘贴功能', '信息', wx.OK | wx.ICON_INFORMATION)

    def on_about(self, event):
        """关于对话框事件处理"""
        info = wx.adv.AboutDialogInfo()
        info.SetName("wxPython 示例应用")
        info.SetVersion("1.0")
        info.SetDescription("这是一个带有菜单栏的 wxPython 示例应用程序")
        info.SetCopyright("(C) 2024")
        wx.adv.AboutBox(info)

if __name__ == '__main__':
    app = wx.App()
    Example(None, title='带菜单栏的 wxPython 应用')
    app.MainLoop()

