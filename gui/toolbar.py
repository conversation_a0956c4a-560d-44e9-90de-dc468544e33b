#!/usr/bin/python
# -*- coding: utf-8 -*-
 
'''
Translated By Achen
2017/9
===
ZetCode wxPython tutorial
 
This example creates a simple toolbar.
 
author: <PERSON>
website: www.zetcode.com
last modified: September 2011
'''
 
import wx
 
class Example(wx.Frame):
     
    def __init__(self, *args, **kwargs):
        super(Example, self).__init__(*args, **kwargs) 
             
        self.InitUI()
         
    def InitUI(self):    
 
        toolbar = self.CreateToolBar()
        qtool = toolbar.AddTool(wx.ID_ANY, 'Quit', wx.Bitmap('icons/texit.png'))
        toolbar.Realize()
 
        self.Bind(wx.EVT_TOOL, self.OnQuit, qtool)
 
        self.SetSize((250, 200))
        self.SetTitle('Simple toolbar')
        self.Centre()
        self.Show(True)
         
    def OnQuit(self, e):
        self.Close()
 
def main():
     
    ex = wx.App()
    Example(None)
    ex.<PERSON>Loop()    
 
 
if __name__ == '__main__':
    main()