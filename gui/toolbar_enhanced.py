#!/usr/bin/python
# -*- coding: utf-8 -*-

'''
Enhanced Toolbar Example
增强版工具栏示例
'''

import wx
import os

class EnhancedToolbarExample(wx.Frame):
    
    def __init__(self, *args, **kwargs):
        super(EnhancedToolbarExample, self).__init__(*args, **kwargs)
        
        self.InitUI()
        
    def InitUI(self):
        
        # 创建工具栏
        toolbar = self.CreateToolBar()
        
        # 获取脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 添加新建工具
        new_bmp = wx.ArtProvider.GetBitmap(wx.ART_NEW, wx.ART_TOOLBAR, (16, 16))
        new_tool = toolbar.AddTool(wx.ID_NEW, '新建', new_bmp, '创建新文件')
        
        # 添加打开工具
        open_bmp = wx.ArtProvider.GetBitmap(wx.ART_FILE_OPEN, wx.ART_TOOLBAR, (16, 16))
        open_tool = toolbar.AddTool(wx.ID_OPEN, '打开', open_bmp, '打开文件')
        
        # 添加保存工具
        save_bmp = wx.ArtProvider.GetBitmap(wx.ART_FILE_SAVE, wx.ART_TOOLBAR, (16, 16))
        save_tool = toolbar.AddTool(wx.ID_SAVE, '保存', save_bmp, '保存文件')
        
        # 添加分隔符
        toolbar.AddSeparator()
        
        # 添加复制工具
        copy_bmp = wx.ArtProvider.GetBitmap(wx.ART_COPY, wx.ART_TOOLBAR, (16, 16))
        copy_tool = toolbar.AddTool(wx.ID_COPY, '复制', copy_bmp, '复制')
        
        # 添加粘贴工具
        paste_bmp = wx.ArtProvider.GetBitmap(wx.ART_PASTE, wx.ART_TOOLBAR, (16, 16))
        paste_tool = toolbar.AddTool(wx.ID_PASTE, '粘贴', paste_bmp, '粘贴')
        
        # 添加分隔符
        toolbar.AddSeparator()
        
        # 添加退出工具 - 尝试使用自定义图标，如果不存在则使用系统图标
        texit_path = os.path.join(script_dir, 'texit.png')
        if os.path.exists(texit_path):
            quit_bmp = wx.Bitmap(texit_path)
        else:
            quit_bmp = wx.ArtProvider.GetBitmap(wx.ART_QUIT, wx.ART_TOOLBAR, (16, 16))
        
        quit_tool = toolbar.AddTool(wx.ID_EXIT, '退出', quit_bmp, '退出应用程序')
        
        # 实现工具栏
        toolbar.Realize()
        
        # 绑定事件
        self.Bind(wx.EVT_TOOL, self.OnNew, new_tool)
        self.Bind(wx.EVT_TOOL, self.OnOpen, open_tool)
        self.Bind(wx.EVT_TOOL, self.OnSave, save_tool)
        self.Bind(wx.EVT_TOOL, self.OnCopy, copy_tool)
        self.Bind(wx.EVT_TOOL, self.OnPaste, paste_tool)
        self.Bind(wx.EVT_TOOL, self.OnQuit, quit_tool)
        
        # 创建主面板
        self.create_main_panel()
        
        # 创建状态栏
        self.CreateStatusBar()
        self.SetStatusText("工具栏示例 - 点击工具栏按钮测试功能")
        
        self.SetSize((600, 400))
        self.SetTitle('增强版工具栏示例')
        self.Centre()
        self.Show(True)
    
    def create_main_panel(self):
        """创建主面板"""
        panel = wx.Panel(self)
        
        # 创建文本控件
        self.text_ctrl = wx.TextCtrl(panel, style=wx.TE_MULTILINE)
        self.text_ctrl.SetValue("这是一个带有工具栏的文本编辑器示例。\n\n" +
                               "工具栏包含以下功能：\n" +
                               "• 新建 - 清空文本\n" +
                               "• 打开 - 打开文件\n" +
                               "• 保存 - 保存文件\n" +
                               "• 复制 - 复制选中文本\n" +
                               "• 粘贴 - 粘贴文本\n" +
                               "• 退出 - 关闭应用程序\n\n" +
                               "尝试选择一些文本，然后点击复制和粘贴按钮。")
        
        # 布局
        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.text_ctrl, 1, wx.EXPAND | wx.ALL, 5)
        panel.SetSizer(sizer)
    
    def OnNew(self, event):
        """新建文件"""
        self.text_ctrl.SetValue("")
        self.SetStatusText("新建文件")
    
    def OnOpen(self, event):
        """打开文件"""
        with wx.FileDialog(self, "打开文件",
                          wildcard="文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                          style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            
            pathname = fileDialog.GetPath()
            try:
                with open(pathname, 'r', encoding='utf-8') as file:
                    self.text_ctrl.SetValue(file.read())
                self.SetStatusText(f"已打开文件: {pathname}")
            except IOError:
                wx.LogError(f"无法打开文件 '{pathname}'")
    
    def OnSave(self, event):
        """保存文件"""
        with wx.FileDialog(self, "保存文件",
                          wildcard="文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                          style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            
            pathname = fileDialog.GetPath()
            try:
                with open(pathname, 'w', encoding='utf-8') as file:
                    file.write(self.text_ctrl.GetValue())
                self.SetStatusText(f"文件已保存到: {pathname}")
            except IOError:
                wx.LogError(f"无法保存文件 '{pathname}'")
    
    def OnCopy(self, event):
        """复制文本"""
        self.text_ctrl.Copy()
        self.SetStatusText("已复制选中文本")
    
    def OnPaste(self, event):
        """粘贴文本"""
        self.text_ctrl.Paste()
        self.SetStatusText("已粘贴文本")
    
    def OnQuit(self, event):
        """退出应用程序"""
        self.Close()

def main():
    app = wx.App()
    EnhancedToolbarExample(None)
    app.MainLoop()

if __name__ == '__main__':
    main()
