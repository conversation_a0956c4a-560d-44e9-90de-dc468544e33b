#!/usr/bin/python
# -*- coding: utf-8 -*-
 
import wx
 
 
class Example(wx.Frame):
            
    def __init__(self, *args, **kw):
        super(Example, self).__init__(*args, **kw) 
         
        self.InitUI()
         
    def InitUI(self):   
 
        pnl = wx.Panel(self)
         
        distros = ['Ubuntu', 'Arch', 'Fedora', 'Debian', 'Mint']
        cb = wx.ComboBox(pnl, pos=(50, 30), choices=distros, 
            style=wx.CB_READONLY)
 
        self.st = wx.StaticText(pnl, label='', pos=(50, 140))
        cb.Bind(wx.EVT_COMBOBOX, self.OnSelect)
         
        self.SetSize((250, 230))
        self.SetTitle('wx.ComboBox')
        self.Centre()
        self.Show(True)          
         
    def OnSelect(self, e):
         
        i = e.GetString()
        self.st.SetLabel(i)
         
def main():
     
    ex = wx.App()
    Example(None)
    ex.<PERSON>Loop()    
 
if __name__ == '__main__':
    main()   