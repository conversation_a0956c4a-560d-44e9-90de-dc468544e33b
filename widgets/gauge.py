#!/usr/bin/python
# -*- coding: utf-8 -*-
 
import wx
 
TASK_RANGE = 50
 
class Example(wx.Frame):
            
    def __init__(self, *args, **kw):
        super(Example, self).__init__(*args, **kw) 
         
        self.InitUI()
         
    def InitUI(self):   
         
        self.timer = wx.Timer(self, 1)
        self.count = 0
 
        self.Bind(wx.EVT_TIMER, self.OnTimer, self.timer)
 
        pnl = wx.Panel(self)
        vbox = wx.BoxSizer(wx.VERTICAL)
        hbox1 = wx.BoxSizer(wx.HORIZONTAL)
        hbox2 = wx.BoxSizer(wx.HORIZONTAL)
        hbox3 = wx.BoxSizer(wx.HORIZONTAL)
 
        self.gauge = wx.Gauge(pnl, range=TASK_RANGE, size=(250, 25))
        self.btn1 = wx.<PERSON><PERSON>(pnl, wx.ID_OK)
        self.btn2 = wx.<PERSON><PERSON>(pnl, wx.ID_STOP)
        self.text = wx.StaticText(pnl, label='Task to be done')
 
        self.Bind(wx.EVT_BUTTON, self.OnOk, self.btn1)
        self.Bind(wx.EVT_BUTTON, self.OnStop, self.btn2)
 
        hbox1.Add(self.gauge, proportion=1, flag=wx.ALIGN_CENTRE)
        hbox2.Add(self.btn1, proportion=1, flag=wx.RIGHT, border=10)
        hbox2.Add(self.btn2, proportion=1)
        hbox3.Add(self.text, proportion=1)
        vbox.Add((0, 30))
        vbox.Add(hbox1, flag=wx.ALIGN_CENTRE)
        vbox.Add((0, 20))
        vbox.Add(hbox2, proportion=1, flag=wx.ALIGN_CENTRE)
        vbox.Add(hbox3, proportion=1, flag=wx.ALIGN_CENTRE)
 
        pnl.SetSizer(vbox)
         
        self.SetSize((300, 200))
        self.SetTitle('wx.Gauge')
        self.Centre()
        self.Show(True)     
 
    def OnOk(self, e):
         
        if self.count >= TASK_RANGE:
            return
 
        self.timer.Start(100)
        self.text.SetLabel('Task in Progress')
 
    def OnStop(self, e):
         
        if self.count == 0 or self.count >= TASK_RANGE or not self.timer.IsRunning():
            return
 
        self.timer.Stop()
        self.text.SetLabel('Task Interrupted')
         
    def OnTimer(self, e):
         
        self.count = self.count + 1
        self.gauge.SetValue(self.count)
         
        if self.count == TASK_RANGE:
 
            self.timer.Stop()
            self.text.SetLabel('Task Completed')
                       
def main():
     
    ex = wx.App()
    Example(None)
    ex.MainLoop()    
 
if __name__ == '__main__':
    main()   